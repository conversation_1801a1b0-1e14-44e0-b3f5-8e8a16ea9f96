//
//  ChargingVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 24/07/21.
//

import UIKit
import Starscream
import Alamofire
import DropDown
import Cosmos
import LinearProgressBar
import LanguageManager_iOS
import ACFloatingTextfield_Swift

class ChargingVC: UIViewController {


    // MARK: - IBOutlets
    @IBOutlet weak var chargingButton: UIButton!
    @IBOutlet weak var carView: UIView!
    @IBOutlet weak var percentLabel: UILabel!
    @IBOutlet weak var startTimeLabel: UILabel!
    @IBOutlet weak var chargingDetailsView: UIView!
    @IBOutlet weak var consumedUnitsLabel: UILabel!
    @IBOutlet weak var totalChargesLabel: UILabel!
    @IBOutlet weak var availableBalanceLabel: UILabel!

    @IBOutlet weak var transactionBGView: UIView!
    @IBOutlet weak var transactionMainView: UIView!
    @IBOutlet weak var transactionTop: UIView!

    @IBOutlet weak var timeStartedLabel: UILabel!
    @IBOutlet weak var timeDurationLabel: UILabel!
    @IBOutlet weak var chargePointLabel: UILabel!
    @IBOutlet weak var totalChargesTotal: UILabel!

    @IBOutlet weak var viewTransactionButton: UIButton!

    @IBOutlet weak var ratingsMainView: UIView!
    @IBOutlet weak var ratingsTopView: UIView!
    @IBOutlet weak var ratingChargeStationNameLabel: UILabel!
    @IBOutlet weak var cosmosRatings: CosmosView!
    @IBOutlet weak var ratingsButton: UIButton!

    @IBOutlet weak var menuButton: UIButton!
    @IBOutlet weak var viewBgMenu: UIView!
    @IBOutlet weak var viewMainMenu: UIView!
    @IBOutlet weak var viewProfile: UIView!

    @IBOutlet weak var lblInitial: UILabel!
    @IBOutlet weak var lblUserName: UILabel!
    @IBOutlet weak var lblBalance: UILabel!
    @IBOutlet weak var tableMenu: UITableView!
    @IBOutlet weak var tableHeight: NSLayoutConstraint!

    @IBOutlet weak var viewBgChargeType: UIView!
    @IBOutlet weak var viewMainChargeType: UIView!

    @IBOutlet weak var viewFullCharged: UIView!
    @IBOutlet weak var viewUnit: UIView!
    @IBOutlet weak var viewTime: UIView!

    @IBOutlet weak var datePickerChargeType: UIDatePicker!
    @IBOutlet weak var txtUnitChargeType: UITextField!
    @IBOutlet weak var lblEstimatedPrice: UILabel!

    @IBOutlet weak var scanQRButton: UIButton!

    @IBOutlet weak var lblVehicle: UILabel!
    @IBOutlet weak var btnVehicle: UIButton!

    @IBOutlet weak var stackNotCharging: UIStackView!
    @IBOutlet weak var viewBgNDCP: UIView!
    @IBOutlet weak var viewMainNDCP: UIView!
    @IBOutlet weak var connectedButton: UIButton!
    @IBOutlet weak var cancelConnectedButton: UIButton!
    @IBOutlet weak var transactionImgView: UIImageView!
    @IBOutlet weak var transactionTitleLabel: UILabel!
    @IBOutlet weak var transactionMsgLabel: UILabel!

    @IBOutlet weak var btnCancelTransactionPop: UIButton!
    @IBOutlet weak var btnSkipRatings: UIButton!

    @IBOutlet weak var viewVehicleSelection: UIView!


    @IBOutlet weak var kwhLabel: UILabel!

    var menuTitleList:[String] = []
    var menuImagesList:[String] = []
    var selectedIndex = Int()
    var chargeType = Int()

    var arrChargingImages:[String] = []
    var timer = Timer()
    var timerCar = Timer()

    var photoCount:Int = 0
    var arrDataOptions:[String] = []
    var socket: WebSocket!
    var strRecData:String = "0"
    var param:[String:Any] = [:]
    var paramViewProfile:[String:Any] = [:]
    var paramRate:[String:Any] = [:]
    var paramTxnInfo:[String:Any] = [:]
    let vehicleDropDown = DropDown()
    var vehicleData:[VehicleDetails] = []
    var strShowMeter:String = "0"

    var transType = String()
    var transValue = String()

    var transData:[TransactionData] = []
    var otherCharge:[OtherCharge] = []

    var selectedChargeTypeValue = String()

    // MARK: - View LifeCycle
    override func viewDidLoad() {
        super.viewDidLoad()

        transactionBGView.isHidden = true

        cosmosRatings.rating = 3
        cosmosRatings.settings.minTouchRating = 0
        cosmosRatings.settings.fillMode = .half

        tableMenu.delegate = self
        tableMenu.dataSource = self

        txtUnitChargeType.delegate = self

        viewBgMenu.isHidden = true

        menuTitleList = ["Profile","Change Language","Get Your RFID Card","News",
                         "Buy Charger","Games","Help","Complaint","About Us"]
        menuImagesList = ["ic_profile","ic_change_language","ic_complain","ic_news","ic_buy_charger","ic_game_icon","ic_help","ic_complain","ic_about"]

        tableHeight.constant = CGFloat(menuTitleList.count * 60)

        setupDropDowns()

        self.viewBgChargeType.isHidden = true
        chargeType = 101

        if #available(iOS 14.0, *) {
            datePickerChargeType.preferredDatePickerStyle = .wheels
//            datePicker.preferredFocusEnvironments
        } else {
            // Fallback on earlier versions
        }

        datePickerChargeType.datePickerMode = .time // setting mode to timer so user can only pick time as you want
//        datePicker.minuteInterval = 30  // with interval of 30
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat =  "HH:mm"
        let min = dateFormatter.date(from: "00:15")      //creating min time
        let max = dateFormatter.date(from: "10:00") //creating max time
        datePickerChargeType.minimumDate = min  //setting min time to picker
        datePickerChargeType.maximumDate = max  //setting max time to picker
        datePickerChargeType.locale = NSLocale(localeIdentifier: "en_GB") as Locale
        datePickerChargeType.date = datePickerChargeType.minimumDate!
        datePickerChargeType.addTarget(self, action: #selector(handleDatePicker(sender:)), for: .valueChanged)

        let nc = NotificationCenter.default
        nc.addObserver(self, selector: #selector(onDidReceiveData), name: Notification.Name(WebSocketDetails.EVENT_SHOW_METER_VALUE), object: nil)
        nc.addObserver(self, selector: #selector(onDidReceiveData1), name: Notification.Name(WebSocketDetails.EVENT_TRANSACTION_STOPPED), object: nil)

        // Connect to WebSocket automatically when page loads
        connectToWebSocket()
    }

    override func viewWillAppear(_ animated: Bool) {

        AppDelegate.shared.tabIndex = 1
        self.percentLabel.text = "0%"
        getWalletBalance()

        getProfileDetails()
        viewBgMenu.isHidden = true
        viewBgChargeType.isHidden = true
        AppDelegate.shared.selectedIndex = 0
        self.tableMenu.reloadData()

        timerCar.invalidate()
        timer.invalidate()
        UserDefaults.standard.set("0", forKey: Constants.IS_CHARGING)

        if AppDelegate.shared.isStartEvent == true {
            stackNotCharging.isHidden = false
            AppDelegate.shared.isStartEvent = false

            AppDelegate.shared.strHMS = "00:00:00"
            timer.invalidate()
            timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(onTransition1), userInfo: nil, repeats: true)

            self.availableBalanceLabel.text = "Available Balance : ₹ 0"
            self.totalChargesLabel.text = "₹ 0"
            self.startTimeLabel.text = "Start Time : \(AppDelegate.shared.strHMS)"
            self.consumedUnitsLabel.text = "0 kWh"

        } else {
            stackNotCharging.isHidden = true
        }

        showHideNDCP()

    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = false
    }

    func showHideNDCP() {
        if AppDelegate.shared.isFromNDCP == true {
            viewBgNDCP.isHidden = false
        } else {
            viewBgNDCP.isHidden = true
        }
    }

    // MARK: - WebSocket Connection
    func connectToWebSocket() {
        print("Connecting to WebSocket automatically on page load")

        // Check if already connected to avoid duplicate connections
        if AppDelegate.shared.socket != nil && AppDelegate.shared.socket.isConnected {
            print("WebSocket is already connected")
            return
        }

        // Create WebSocket connection
        var request = URLRequest(url: URL(string: Constants.WEBSOCKET_URL)!)
        request.timeoutInterval = 1
        AppDelegate.shared.socket = WebSocket(request: request)
        AppDelegate.shared.socket.delegate = self
        AppDelegate.shared.socket.connect()
        print("WebSocket connection established on page load")
    }

    @objc func onDidReceiveData(_ notification: Notification) {

        if strRecData == "0" {
            print("onDidReceiveData called")
            timer.invalidate()
            strRecData = "1"
        }

        chargingButton.isUserInteractionEnabled = true
        chargingButton.setTitle("Stop Charging".localiz(), for: .normal)

        self.startTimeLabel.text = "Start Time : \(AppDelegate.shared.strHMS)"
        self.consumedUnitsLabel.text = "\(AppDelegate.shared.strUsedUnit) kWh"
        self.availableBalanceLabel.text = "Available Balance : ₹ \(AppDelegate.shared.strAvailBalance.roundedDouble(digits: 2))"
        self.totalChargesLabel.text = "₹ \(AppDelegate.shared.strCharges.roundedDouble(digits: 2))"

        let dateFormatter1 = DateFormatter()
        dateFormatter1.timeZone = .current
        dateFormatter1.locale = Locale(identifier: "en_US")
        dateFormatter1.dateFormat = "dd MMM yyyy"

        let dateFormatter2 = DateFormatter()
        dateFormatter2.timeZone = .current
        dateFormatter2.locale = Locale(identifier: "en_US")
        dateFormatter2.dateFormat = "hh:mm a"
        timeDurationLabel.text = "\(AppDelegate.shared.strHMS)"



      //  let timeStampTxnID = Date(timeIntervalSince1970: Double("\(AppDelegate.shared.strTxnDuration)")!)
        //"\(AppDelegate.shared.strTxnDuration)".millisecondsSince1970
      //  print("timeStampTxnID:-",timeStampTxnID)

//        let now = Date()
//        let formatter = DateFormatter()
//        formatter.timeZone = TimeZone.current
//        formatter.dateFormat = "yyyy-MM-dd hh:mm:ss"
//        let dateString = formatter.string(from: now)

//        let strStartTime = "\(timeStampTxnID)"//.convertDateString(fromFormat: "yyyy-MM-dd HH:mm:ss Z", toFormat: "hh:mm a")

        timeStartedLabel.text = "\(AppDelegate.shared.strTxnDuration)"

        ratingChargeStationNameLabel.text = AppDelegate.shared.strChargePoint

        chargePointLabel.text = "\(AppDelegate.shared.strChargePoint)"
        totalChargesTotal.text = "₹ \(AppDelegate.shared.strCharges.roundedDouble(digits: 2))"

    }

    @objc func onDidReceiveData1(_ notification: Notification) {

        timer.invalidate()
        print("onDidReceiveData1 called")

        stackNotCharging.isHidden = true

//        status 0 insu bal
//        1 succuessful
//        2 ev dis

        if "\(UserDefaults.standard.value(forKey: Constants.STR_SHOWMETER)!)" == "0" {
            stackNotCharging.isHidden = true
            timer.invalidate()
        } else {
            stackNotCharging.isHidden = false
        }

        transactionBGView.isHidden = false
        transactionMainView.isHidden = false
        ratingsMainView.isHidden = true

        if AppDelegate.shared.strStopStatus == "1" {
            self.transactionTitleLabel.text = "SUCCESSFUL"
            self.transactionMsgLabel.text = "Your transaction was successful"
        } else if AppDelegate.shared.strStopStatus == "2" {
            self.transactionTitleLabel.text = "DISCONNECTED"
            self.transactionMsgLabel.text = "Your transaction was successful but EV disconnected"
        } else {
            self.transactionTitleLabel.text = "INSUFFICIENT BALANCE"
            self.transactionMsgLabel.text = "Your transaction has stopped due to insufficient balance"
        }

        chargingButton.isUserInteractionEnabled = true
        chargingButton.setTitle("Start Charging".localiz(), for: .normal)
        chargingButton.setTitleColor(UIColor.white, for: .normal)

        UserDefaults.standard.set("0", forKey: Constants.STR_SHOWMETER)

    }



    @objc func handleDatePicker(sender: UIDatePicker) {
//        intDateChanged = 1
        let dateFormatter = DateFormatter()
        dateFormatter.timeZone = .current
        dateFormatter.locale = Locale(identifier: "en_US")
        dateFormatter.dateFormat = "HH:mm"
//        btnSelectTime.setTitle(dateFormatter.string(from: sender.date), for: .normal)
    }

    override func viewDidLayoutSubviews() {

        viewMainChargeType.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        scanQRButton.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        chargingButton.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
//        carView.addBorder(color: Constants.textBorderColor!, width: Int(0.5))
//        carView.shadowWithCRadius(radius: carView.frame.height/2, color: Constants.secondaryGrayText!)
        chargingDetailsView.maskClipCorner(cornerRadius: 12)

        transactionMainView.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewTransactionButton.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        ratingsMainView.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        ratingsButton.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        menuButton.shadowWithCRadius(radius: menuButton.frame.height/2, color: Constants.secondaryGrayText!)
        menuButton.maskClipCorner(cornerRadius: Int(menuButton.frame.height/2))

        viewProfile.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        viewProfile.maskClipCorner(cornerRadius: 10)

        vehicleDropDown.backgroundColor = .white
        vehicleDropDown.textColor = .black
        vehicleDropDown.selectedTextColor = .white
        vehicleDropDown.selectionBackgroundColor = Constants.primaryColor!

        viewMainNDCP.roundCorners(corners: [.topLeft,.topRight], radius: 12)

        connectedButton.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        lblInitial.maskClipCorner(cornerRadius: Int(lblInitial.frame.height/2))

        viewVehicleSelection.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        viewVehicleSelection.maskClipCorner(cornerRadius: 12)

        btnCancelTransactionPop.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        btnSkipRatings.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

    }

    func openCloseMenu() {
        UIView.animate(withDuration: 2, delay: 0, options: [.curveEaseOut], animations: {
            if self.viewBgMenu.isHidden == true {
                self.viewBgMenu.isHidden = false
            } else {
                self.viewBgMenu.isHidden = true
            }
        }, completion: nil)
    }

    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        let touch = touches.first!
        if(touch.view == viewBgMenu){
            openCloseMenu()
        }
    }

    @objc func onTransition() {
        UIView.animate(withDuration: 1.0, animations: {() -> Void in
            self.carView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        }, completion: {(_ finished: Bool) -> Void in
            UIView.animate(withDuration: 1.0, animations: {() -> Void in
                self.carView.transform = CGAffineTransform(scaleX: 1, y: 1)
            })
        })
    }
    @objc func onTransition1() {

        let dateFormatter = DateFormatter()
        dateFormatter.timeZone = .current
        dateFormatter.locale = Locale(identifier: "en_US")
        dateFormatter.dateFormat = "HH:mm:ss"
        let date1 = dateFormatter.date(from: AppDelegate.shared.strHMS)
//        print("date1:-",date1!)

        let calendar = Calendar.current
        let date = calendar.date(byAdding: .second, value: 1, to: date1!)
//        print("date:-",date!)

        let strDate = dateFormatter.string(from: date!)
        self.startTimeLabel.text = "Start Time : \(strDate)"
        AppDelegate.shared.strHMS = "\(strDate)"

//        UIView.animate(withDuration: 1.0, animations: {() -> Void in
//            self.carView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
//        }, completion: {(_ finished: Bool) -> Void in
//            UIView.animate(withDuration: 1.0, animations: {() -> Void in
//                self.carView.transform = CGAffineTransform(scaleX: 1, y: 1)
//            })
//        })

    }

    //MARK: -  DropDown Methods
    func setupDropDowns() {
        setupVehicleDropDown()
    }

    func setupVehicleDropDown() {
        UserDefaults.standard.set("1", forKey: Constants.ISCHARGING_DROPDOWN_CENTER)
        vehicleDropDown.anchorView = btnVehicle
        vehicleDropDown.bottomOffset = CGPoint(x: 0, y: btnVehicle.bounds.height)
        vehicleDropDown.selectionAction = { [weak self] (index, item) in
            self!.lblVehicle.text = "\(item)"
            // Store the enhanced display text for consistency
            UserDefaults.standard.set("\(item)", forKey: Constants.VEHICLE_NAME)
            UserDefaults.standard.set("\(self!.vehicleData[index].vehicle_id)", forKey: Constants.VEHICLE_ID)
            AppDelegate.shared.strVehicleID = "\(self!.vehicleData[index].vehicle_id)"
        }
    }


    // MARK: - Button Actions
    @IBAction func selectVehicleTapped(_ sender: UIButton) {
        vehicleDropDown.show()
    }

    @IBAction func chargingTapped(_ sender: UIButton) {
        openChargeType()
//        if chargingButton.titleLabel?.text == "Start Charging" ||
//            chargingButton.titleLabel?.text == "चार्ज करना शुरू करें" ||
//            chargingButton.titleLabel?.text == "चार्जिंग सुरू करा" {
//            if self.lblVehicle.text == "" || self.lblVehicle.text == "Select Vehicle".localiz() {
//                self.alert(title: "Select Vehicle".localiz(), message: "Please select vehicle to start charging".localiz())
//            } else {
//                openChargeType()
////                viewBgCharging.isHidden = false
////                viewMainChargeType.isHidden = false
////                viewMainTime.isHidden = true
////                viewMainSelectTime.isHidden = true
////                viewMainUnit.isHidden = true
////                viewBgChargeType.isHidden = false
//            }
//        } else {
//            openChargeType()
//        }
    }

    @IBAction func closeTransactionTapped(_ sender: UIButton) {
        transactionBGView.isHidden = true
    }

    @IBAction func viewTransactionTapped(_ sender: UIButton) {

        // Set type ID to "1" for charging transactions
        AppDelegate.shared.strTypeID = "1"
        let vc = TransactionDetailsVC.instantiate(appStoryboard: .Wallet)
        navigationController?.pushViewController(vc, animated: true)
//        transactionBGView.isHidden = true
    }

    @IBAction func closeRatingsTapped(_ sender: UIButton) {
        transactionBGView.isHidden = true
    }

    @IBAction func ratingsButtonTapped(_ sender: UIButton) {
        transactionBGView.isHidden = true
        getAddRate()
    }

    @IBAction func menuButtonTapped(_ sender: UIButton) {
        AppDelegate.shared.isMenuOpen = 1
        let notificationShowHideMenu = Notification.Name("notificationShowHideMenu")
        NotificationCenter.default.post(name: notificationShowHideMenu, object: nil)
        openCloseMenu()
    }

    @IBAction func chargeTypeCloseTapped(_ sender: UIButton) {
        viewBgChargeType.isHidden = true
    }

    @IBAction func scanQRTapped(_ sender: UIButton) {
        startStopCharging()
    }

    @IBAction func chargeTypeTapped(_ sender: UIButton) {
        if chargeType == 101 {
            datePickerChargeType.isHidden = true
            txtUnitChargeType.isHidden = true
            kwhLabel.isHidden = true
            lblEstimatedPrice.isHidden = true
        } else if chargeType == 102 {
            datePickerChargeType.isHidden = true
            txtUnitChargeType.isHidden = false
            kwhLabel.isHidden = false
            lblEstimatedPrice.isHidden = false
        } else if chargeType == 103 {
            datePickerChargeType.isHidden = false
            txtUnitChargeType.isHidden = true
            kwhLabel.isHidden = true
            lblEstimatedPrice.isHidden = false
        }
        print("chargeType:-",sender.tag)
        highlightChargeType(type: sender.tag)
    }

    @IBAction func connectedButtonTapped(_ sender: Any) {
        self.remoteStartEvent()
        self.viewBgNDCP.isHidden = true
    }

    @IBAction func cancelConnectedButtonTapped(_ sender: UIButton) {
        viewBgNDCP.isHidden = true
    }

    @IBAction func logoutTapped(_ sender: UIButton) {
        viewBgMenu.isHidden = true
        let alertController = UIAlertController(title: "Logout".localiz(), message: "Logout Message".localiz(), preferredStyle: .alert)

        // Create the actions
        let cancelAction = UIAlertAction(title: "NO".localiz(), style: UIAlertAction.Style.cancel) {
            UIAlertAction in
            print("Cancel Pressed")
        }

        let okAction = UIAlertAction(title: "YES".localiz(), style: UIAlertAction.Style.default) {
            UIAlertAction in
            print("OK Pressed")
            AppDelegate.shared.apiKeyLogout()
        }

        // Add the actions
        alertController.addAction(cancelAction)
        alertController.addAction(okAction)

        // Present the controller
        self.present(alertController, animated: true, completion: nil)
    }


    @IBAction func cancelTransactionPopTapped(_ sender: UIButton) {
        cosmosRatings.rating = 0
        transactionBGView.isHidden = false
        transactionMainView.isHidden = true
        ratingsMainView.isHidden = false
        stackNotCharging.isHidden = true
    }


    @IBAction func skipRatingsButton(_ sender: UIButton) {
        transactionBGView.isHidden = true
        transactionMainView.isHidden = true
        ratingsMainView.isHidden = true
        stackNotCharging.isHidden = true
    }


    //MARK: - Remote Start Event
    func remoteStartEvent() {
//        remotestart - "payload":"cp011_1","mobile":"7383855813"
        let dic = [ "event":WebSocketDetails.EVENT_REMOTE_START_TRANSACTION,
                    "payload":"\(UserDefaults.standard.value(forKey: Constants.SCAN_QRCODE)!)",
                    "mobile":"\(UserDefaults.standard.value(forKey: Constants.PHONE)!)"]
        let jsonData1 = try! JSONSerialization.data(withJSONObject: dic, options: [])
        let str = String(data: jsonData1, encoding: .utf8)
        AppDelegate.shared.socket.write(string: str!)
    }

    func remoteStopEvent() {
        let dic = [ "event":WebSocketDetails.EVENT_REMOTE_STOP_TRANSACTION,
                    "payload":"\(UserDefaults.standard.value(forKey: Constants.SCAN_QRCODE)!)",
                    "user_id":"\(UserDefaults.standard.object(forKey: Constants.USER_ID)!)"]
        let jsonData1 = try! JSONSerialization.data(withJSONObject: dic, options: [])
        let str = String(data: jsonData1, encoding: .utf8)
        print("remoteStopEvent:-",str)
        AppDelegate.shared.socket.write(string: str!)
    }

    func highlightChargeType(type: Int) {

        if type == 101 {
            viewFullCharged.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
            viewFullCharged.maskClipCorner(cornerRadius: 8)

            viewUnit.addBorder(color: .white, width: Int(1))
            viewUnit.maskClipCorner(cornerRadius: 8)

            viewTime.addBorder(color: .white, width: Int(1))
            viewTime.maskClipCorner(cornerRadius: 8)

            datePickerChargeType.isHidden = true
            txtUnitChargeType.isHidden = true
            kwhLabel.isHidden = true
            lblEstimatedPrice.isHidden = true

            AppDelegate.shared.transType = "1"
            AppDelegate.shared.transValue = "0"
            selectedChargeTypeValue = "1"

        } else if type == 102 {
            viewFullCharged.addBorder(color: .white, width: Int(1))
            viewFullCharged.maskClipCorner(cornerRadius: 8)

            viewUnit.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
            viewUnit.maskClipCorner(cornerRadius: 8)

            viewTime.addBorder(color: .white, width: Int(1))
            viewTime.maskClipCorner(cornerRadius: 8)

            datePickerChargeType.isHidden = true
            txtUnitChargeType.isHidden = false
            kwhLabel.isHidden = false
            lblEstimatedPrice.isHidden = true
            selectedChargeTypeValue = "3"

            let str = "\(self.txtUnitChargeType.text!)"
            print("txtUnitChargeType:-",str)
            let decimalCharacters = CharacterSet.decimalDigits
            let decimalRange = str.rangeOfCharacter(from: decimalCharacters)
            if decimalRange != nil {
                print("Numbers found")

                if Int(str)! <= 50 && Int(str)! >= 3 {
                    print("Unit Set")
                    AppDelegate.shared.transType = "3"
                    AppDelegate.shared.transValue = "\(self.txtUnitChargeType.text!)"

                } else {
                    print("Unit limit must be between 3 and 50")
//                    self.alert(title: "Unit", message: "Unit limit must be between 3 and 50")
                }
            } else {
                print("Please enter valid unit")
//                self.alert(title: "Unit", message: "Please enter valid unit")
            }

        } else {
            viewFullCharged.addBorder(color: .white, width: Int(1))
            viewFullCharged.maskClipCorner(cornerRadius: 8)

            viewUnit.addBorder(color: .white, width: Int(1))
            viewUnit.maskClipCorner(cornerRadius: 8)

            viewTime.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
            viewTime.maskClipCorner(cornerRadius: 8)

            datePickerChargeType.isHidden = false
            txtUnitChargeType.isHidden = true
            kwhLabel.isHidden = true
            lblEstimatedPrice.isHidden = true

            let dateFormatter = DateFormatter()
            dateFormatter.timeZone = .current
            dateFormatter.locale = Locale(identifier: "en_US")
            dateFormatter.dateFormat = "HH:mm"
//            btnSelectTime.setTitle(dateFormatter.string(from: datePickerChargeType.date), for: .normal)

            let calendar = Calendar.current
            let comp = calendar.dateComponents([.hour, .minute], from: datePickerChargeType.date)
            let hour = comp.hour ?? 0
            let minute = comp.minute ?? 0
            print("minute:-",hour)
            print("hour:-",hour)
            let finalMinut:Int = (hour * 60) + minute
            print("finalMinut:-", finalMinut)

            AppDelegate.shared.transType = "2"
            AppDelegate.shared.transValue = "\(finalMinut)"
            selectedChargeTypeValue = "2"
        }
    }

    //MARK: - Charging Start Stop
    func openChargeType() {
        if chargingButton.titleLabel?.text == "Start Charging" ||
            chargingButton.titleLabel?.text == "चार्ज करना शुरू करें" ||
            chargingButton.titleLabel?.text == "चार्जिंग सुरू करा" {
            if self.lblVehicle.text == "" || self.lblVehicle.text == "Select Vehicle".localiz() {
                self.alert(title: "Select Vehicle".localiz(), message: "Please select vehicle to start charging".localiz())
            } else {
                UserDefaults.standard.set("0", forKey: Constants.IS_TXN_STOPPED)
                // WebSocket connection is now handled automatically on page load
                viewBgChargeType.isHidden = false
                highlightChargeType(type: 101)
            }
        } else {
            startStopCharging()
        }
    }

    func startStopCharging() {

        if "\(UserDefaults.standard.value(forKey: Constants.IS_NDCP)!)" == "1" {
            print("remoteStopEvent called")
            self.remoteStopEvent()
        } else {
            if selectedChargeTypeValue == "3" {

                print("selectedChargeTypeValue == 3")

                let str = "\(self.txtUnitChargeType.text!)"
                print("txtUnitChargeType:-",str)
                let decimalCharacters = CharacterSet.decimalDigits
                let decimalRange = str.rangeOfCharacter(from: decimalCharacters)
                if decimalRange != nil {
                    print("Numbers found")

                    if Int(str)! <= 50 && Int(str)! >= 3 {
                        print("Unit Set")
                        AppDelegate.shared.transType = "3"
                        AppDelegate.shared.transValue = "\(self.txtUnitChargeType.text!)"
                        let vc = ScanQRVC.instantiate(appStoryboard: .Charging)
                        self.navigationController?.pushViewController(vc, animated: true)
                    } else {
                        print("Unit limit must be between 3 and 50")
                        self.alert(title: "Unit", message: "Unit limit must be between 3 and 50")
                    }
                } else {
                    print("Please enter valid unit")
                    self.alert(title: "Unit", message: "Please enter valid unit")
                }
            } else {
                let vc = ScanQRVC.instantiate(appStoryboard: .Charging)
                self.navigationController?.pushViewController(vc, animated: true)
            }

//            let dic = [ "event":WebSocketDetails.EVENT_SCANQR,
//                        "user_id":"\(UserDefaults.standard.object(forKey: Constants.USER_ID)!)",
//                        "payload":"\(UserDefaults.standard.object(forKey: Constants.SCAN_QRCODE)!)",
//                        "trans_value":self.transValue,
//                        "trans_type":self.transType]
//            print(dic)
//            let jsonData1 = try! JSONSerialization.data(withJSONObject: dic, options: [])
//            print(jsonData1)
//            let str1 = String(data: jsonData1, encoding: .utf8)
//            print(str1!)
//
//            if AppDelegate.shared.socket.isConnected {
//                AppDelegate.shared.socket.write(string: str1!)
//            }
        }

//        if chargingButton.titleLabel?.text == "Start Charging" ||
//            chargingButton.titleLabel?.text == "चार्ज करना शुरू करें" ||
//            chargingButton.titleLabel?.text == "चार्जिंग सुरू करा" {
//            if self.lblVehicle.text == "" || self.lblVehicle.text == "Select Vehicle".localiz() {
//                self.alert(title: "Select Vehicle".localiz(), message: "Please select vehicle to start charging".localiz())
//            } else {
//                viewBgChargeType.isHidden = false
//                highlightChargeType(type: 101)
////                let vc = ScanQRVC.instantiate(appStoryboard: .Charging)
////                self.navigationController?.pushViewController(vc, animated: true)
//            }
//        } else {
//
//            if "\(UserDefaults.standard.value(forKey: Constants.IS_NDCP)!)" == "1" {
//                self.remoteStopEvent()
//            } else {
//                let dic = [ "event":WebSocketDetails.EVENT_SCANQR,
//                            "user_id":"\(UserDefaults.standard.object(forKey: Constants.USER_ID)!)",
//                            "payload":"\(UserDefaults.standard.object(forKey: Constants.SCAN_QRCODE)!)",
//                            "trans_value":self.transValue,
//                            "trans_type":self.transType]
//                print(dic)
//                let jsonData1 = try! JSONSerialization.data(withJSONObject: dic, options: [])
//                print(jsonData1)
//                let str1 = String(data: jsonData1, encoding: .utf8)
//                print(str1!)
//
//                if AppDelegate.shared.socket.isConnected {
//                    AppDelegate.shared.socket.write(string: str1!)
//                }
//            }
//        }
    }


    // MARK: - Convert to Dictionary
    func convertToDictionary(text: String) -> [String: Any]? {
        if let data = text.data(using: .utf8) {
            do {
                return try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
            } catch {
                print(error.localizedDescription)
            }
        }
        return nil
    }

    //MARK: - Webservice Methods
    func getTransVehicle(transID:String) {

        let url = Constants.BASE_URL + API.GET_TRANSACTION_VEHICLE
        param = ["vehicle_id":"\(AppDelegate.shared.strVehicleID)",
                        "transaction_pk":"\(transID)"]
//        print("GET_TRANSACTION_VEHICLE:-",dataDict)
//        let jsonData1 = try! JSONSerialization.data(withJSONObject: dataDict, options: [])
//        let str = String(data: jsonData1, encoding: .utf8)
//        param = ["data":str!]
        print("GET_TRANSACTION_VEHICLE PARAM:-",param)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: param, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        print("GET_TRANSACTION_VEHICLE Response:-",response)
        AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:

//                print(response)
                let JSON = response.value as! NSDictionary
//                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
//                    let dicData = JSON["pay_details"] as! NSDictionary
//                    print(dicData)
                    UserDefaults.standard.set("1", forKey: Constants.STR_SHOWMETER)
                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func getProfileDetails() {

        let url = Constants.BASE_URL + API.VIEW_PROFILE
      //  print("🔍 Vehicle API URL: \(url)")
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
     //   print("🔑 Authorization Header: \(headers)")

        AF.request(url, method: .get, parameters: nil, encoding: URLEncoding.default, headers: headers).responseJSON { response in
          //  print("📡 Vehicle API Raw Response: \(response)")
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
          //      print("✅ Vehicle API Success")
                let JSON = response.value as! NSDictionary
          //      print("📊 Vehicle API JSON: \(JSON)")

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
               //     print("🟢 Vehicle API Status OK")
                    let jsonData = JSON["data"] as! NSDictionary
               //     print("🚗 Vehicle Data Structure: \(jsonData.allKeys)")

                    if let vehicleDataArray = jsonData["vehicle_data"] as? NSArray {
               //         print("🚙 Vehicle Array Count: \(vehicleDataArray.count)")
                //        print("🚙 First Vehicle Item: \(vehicleDataArray.firstObject ?? "No vehicles")")
                    } else {
                        print("⚠️ Vehicle Data Missing or Format Changed")
                    }

                    self.vehicleData.removeAll()
                    self.vehicleDropDown.dataSource.removeAll()

                    for item in jsonData["vehicle_data"] as! NSArray {
                        let vehicleItem = item as! NSDictionary
                   //     print("🔍 Processing Vehicle: \(vehicleItem)")
                        self.vehicleData.append(VehicleDetails(dic: vehicleItem))
                    }

                    for item in self.vehicleData {
                  //      print("📝 Adding to dropdown: \(item.model_text)")
                        // Create enhanced display format: "Model (Registration)"
                        let displayText = item.reg_num.isEmpty ? item.model_text : "\(item.model_text) (\(item.reg_num))"
                        self.vehicleDropDown.dataSource.append(displayText)
                    }

                //    print("📋 Final dropdown data: \(self.vehicleDropDown.dataSource)")

                    if self.vehicleDropDown.dataSource.count >= 1 {
                        self.lblVehicle.text = self.vehicleDropDown.dataSource[0]
                        UserDefaults.standard.set("\(self.lblVehicle.text!)", forKey: Constants.VEHICLE_NAME)
                        UserDefaults.standard.set("\(self.vehicleData[0].vehicle_id)", forKey: Constants.VEHICLE_ID)
                        AppDelegate.shared.strVehicleID = "\(self.vehicleData[0].vehicle_id)"
                //        print("🚗 Selected first vehicle: ID=\(self.vehicleData[0].vehicle_id), Name=\(self.vehicleDropDown.dataSource[0])")
                    } else {
                        print("⚠️ No vehicles available in dropdown")
                    }

                    self.vehicleDropDown.reloadAllComponents()

                } else {
                    print("❌ Vehicle API Error: Code=\(JSON["code"]!), Status=\(JSON["status"]!)")
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print("❌ Vehicle API Request Failed: \(error)")
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func getWalletBalance() {

        //AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.WALLET_BALANCE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
//                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary
//                    print(jsonData)

                    self.lblInitial.text = "\(jsonData["user_name"]!)".prefix(1).uppercased()
                    self.lblUserName.text = "\(jsonData["user_name"]!)".capitalizingFirstLetter()
                    self.lblBalance.text = "Avail Balance :".localiz() + " ₹\(jsonData["balance"]!)"

                } else {
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func getAddRate() {

        let url = Constants.BASE_URL + API.ADD_RATE
//        let dateExpire:Date = Date()
//        let timeStampTxnID = "\(dateExpire.millisecondsSince1970)"+"-"+"\(UserDefaults.standard.value(forKey: Constants.USER_ID)!)"
//        print(timeStampTxnID)
//        let dateFormatter1 = DateFormatter()
//        dateFormatter1.timeZone = .current
//        dateFormatter1.locale = Locale(identifier: "en_US")
//        dateFormatter1.dateFormat = "yyyy-MM-dd HH:mm:ss"
//        let strDate = dateFormatter1.string(from: Date())
//        print(strDate)

        paramRate = [   "transaction_id"    :   AppDelegate.shared.strTransactionID,
                        "station_id"        :   "\(AppDelegate.shared.strCSID)",
                        "rating"            :   "\(cosmosRatings.rating)"]

        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramRate, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:

                print(response)
                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    self.stackNotCharging.isHidden = true
                } else {
                    self.alert(title: "Failed", message: "\(JSON["msg"]!)")
                    AppDelegate.shared.hideHUD()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func getTransactionInfo() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.GET_TRANSACTION_INFO
        paramTxnInfo = ["transaction_id":"\(AppDelegate.shared.strTransactionID)"]
        print("🔍 Transaction Info API URL: \(url)")
        print("📝 Transaction Info Params: \(paramTxnInfo)")
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]

        AF.request(url, method: .post, parameters: paramTxnInfo, encoding: URLEncoding.default, headers: headers).responseData { response in
            // Print raw response first
            print("📡 Transaction API Response Status: \(String(describing: response.response?.statusCode))")
            self.printRawResponse(data: response.data)

            // Then try to parse as JSON
            AppDelegate.shared.hideHUD()

            if let data = response.data {
                do {
                    let JSON = try JSONSerialization.jsonObject(with: data) as? NSDictionary
                    print("✅ Transaction API JSON Parsed: \(String(describing: JSON))")
                    // Rest of your code with JSON...

                } catch {
                    print("❌ Transaction API JSON Parsing Error: \(error)")
                    self.alert(title: "", message: "\(error.localizedDescription)")
                }
            } else if let error = response.error {
                print("❌ Transaction API Request Error: \(error)")
                self.alert(title: "", message: "\(error.localizedDescription)")
            }
        }
    }
    // Add this helper method to print raw response data
    func printRawResponse(data: Data?) {
        if let data = data, let rawString = String(data: data, encoding: .utf8) {
            print("Raw API Response: \(rawString)")
        } else {
            print("Raw API Response: Unable to convert data to string")
        }
    }
}
extension ChargingVC: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 55
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return menuTitleList.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {

        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! MenuCell
        cell.lblMenu.text = "\(self.menuTitleList[indexPath.row])".localiz()
        cell.imgMenu.image = UIImage(named: "\(self.menuImagesList[indexPath.row])")

        if indexPath.row+1 == selectedIndex {
            cell.lblMenu.textColor = Constants.primaryColor
            cell.imgMenu!.image = cell.imgMenu!.image?.withRenderingMode(.alwaysTemplate)
            cell.imgMenu!.tintColor = Constants.primaryColor
        } else {
            cell.lblMenu.textColor = Constants.menuTextColor
            cell.imgMenu!.image = cell.imgMenu!.image?.withRenderingMode(.alwaysTemplate)
            cell.imgMenu!.tintColor = Constants.menuIconColor
        }

        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {

        AppDelegate.shared.isMenuOpen = 0
        AppDelegate.shared.selectedIndex = indexPath.row+1
        openCloseMenu()
        self.tableMenu.reloadData()

        if AppDelegate.shared.selectedIndex == 1 {
            let vc = ProfileDetailsVC.instantiate(appStoryboard: .Profile)
            navigationController?.pushViewController(vc, animated: true)
        } else if AppDelegate.shared.selectedIndex == 2 {
            let vc = LanguageVC.instantiate(appStoryboard: .Language)
            navigationController?.pushViewController(vc, animated: true)
        } else if AppDelegate.shared.selectedIndex == 3 {
            AppDelegate.shared.tabIndex = 1
            let vc = IssueNewCardVC.instantiate(appStoryboard: .RFID)
            navigationController?.pushViewController(vc, animated: true)
        } else if AppDelegate.shared.selectedIndex == 4 {
            let vc = NewsVC.instantiate(appStoryboard: .Help)
            navigationController?.pushViewController(vc, animated: true)
        } else if AppDelegate.shared.selectedIndex == 5 {
            let vc = BuyChargerVC.instantiate(appStoryboard: .Help)
            navigationController?.pushViewController(vc, animated: true)
        } else if AppDelegate.shared.selectedIndex == 6 {
            let vc = GameListVC.instantiate(appStoryboard: .Help)
            navigationController?.pushViewController(vc, animated: true)
        } else if AppDelegate.shared.selectedIndex == 7 {
            let vc = HelpVC.instantiate(appStoryboard: .Help)
            navigationController?.pushViewController(vc, animated: true)
        } else if AppDelegate.shared.selectedIndex == 8 {
            let vc = ComplainListVC.instantiate(appStoryboard: .Complain)
            navigationController?.pushViewController(vc, animated: true)
        } else if AppDelegate.shared.selectedIndex == 9 {
            let vc = AboutUsVC.instantiate(appStoryboard: .Help)
            navigationController?.pushViewController(vc, animated: true)
        }
    }
}
extension ChargingVC: WebSocketDelegate {

    func websocketDidConnect(socket: WebSocketClient) {
        print("websocket is connected")

        let dic = [ "event":WebSocketDetails.EVENT_HANDSHAKE,
                    "user_mobile":"\(UserDefaults.standard.value(forKey: Constants.PHONE)!)",
                    "payload":""]
        let jsonData1 = try! JSONSerialization.data(withJSONObject: dic, options: [])
        let str = String(data: jsonData1, encoding: .utf8)
        AppDelegate.shared.socket.write(string: str!)
    }

    func websocketDidDisconnect(socket: WebSocketClient, error: Error?) {
        if let e = error as? WSError {
            print("websocket is disconnected: \(e.message)")
        } else if let e = error {
            print("websocket is disconnected: \(e.localizedDescription)")
        } else {
            print("websocket disconnected")
        }
        let vc = ConnectionLostVC.instantiate(appStoryboard: .Charging)
        navigationController?.pushViewController(vc, animated: true)
    }

    func websocketDidReceiveMessage(socket: WebSocketClient, text: String) {

        print("Received text: \(text)")

        let dict = convertToDictionary(text: text)

        if "\(dict!["event"]!)" == WebSocketDetails.EVENT_HANDSHAKE && "\(dict!["type"]!)" == "success" {

            let dict = convertToDictionary(text: text)
            let dictPayload = dict!["payload"] as! [String:Any]
            print(dictPayload)

            if "\(dict!["type"]!)" == "success" {
                UserDefaults.standard.set("0", forKey: Constants.IS_TXN_STOPPED)
                if dictPayload["user_id"] != nil {
                    UserDefaults.standard.set("\(dictPayload["user_id"]!)", forKey: Constants.USER_ID)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.USER_ID)
                }

                if dictPayload["user_email"] != nil {
                    UserDefaults.standard.set("\(dictPayload["user_email"]!)", forKey: Constants.USER_EMAIL)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.USER_EMAIL)
                }

                if dictPayload["first_name"] != nil {
                    UserDefaults.standard.set("\(dictPayload["first_name"]!)", forKey: Constants.FIRST_NAME)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.FIRST_NAME)
                }

                if dictPayload["last_name"] != nil {
                    UserDefaults.standard.set("\(dictPayload["first_name"]!)", forKey: Constants.LAST_NAME)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.LAST_NAME)
                }

                if dictPayload["birth_day"] != nil {
                    UserDefaults.standard.set("\(dictPayload["birth_day"]!)", forKey: Constants.BIRTH_DAY)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.BIRTH_DAY)
                }

                if dictPayload["sex"] != nil {
                    UserDefaults.standard.set("\(dictPayload["sex"]!)", forKey: Constants.SEX)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.SEX)
                }

                if dictPayload["balance"] != nil {
                    UserDefaults.standard.set("\(dictPayload["balance"]!)", forKey: Constants.BALANCE)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.BALANCE)
                }

                if dictPayload["house_number"] != nil {
                    UserDefaults.standard.set("\(dictPayload["house_number"]!)", forKey: Constants.HOUSE_NUMBER)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.HOUSE_NUMBER)
                }

                if dictPayload["street_name"] != nil {
                    UserDefaults.standard.set("\(dictPayload["street_name"]!)", forKey: Constants.STREET_NAME)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.STREET_NAME)
                }

                if dictPayload["pincode"] != nil {
                    UserDefaults.standard.set("\(dictPayload["pincode"]!)", forKey: Constants.PINCODE)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.PINCODE)
                }

                if dictPayload["city"] != nil {
                    UserDefaults.standard.set("\(dictPayload["city"]!)", forKey: Constants.CITY)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.CITY)
                }

                if dictPayload["country"] != nil {
                    UserDefaults.standard.set("\(dictPayload["country"]!)", forKey: Constants.COUNTRY)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.COUNTRY)
                }

                if dictPayload["ocpp_tag_pk"] != nil {
                    UserDefaults.standard.set("\(dictPayload["ocpp_tag_pk"]!)", forKey: Constants.OCPP_TAG_PK)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.OCPP_TAG_PK)
                }

                if dictPayload["ocpp_blocked"] != nil {
                    UserDefaults.standard.set("\(dictPayload["ocpp_blocked"]!)", forKey: Constants.OCPP_BLOCKED)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.OCPP_BLOCKED)
                }

                if dictPayload["ocpp_id_tag"] != nil {
                    UserDefaults.standard.set("\(dictPayload["ocpp_id_tag"]!)", forKey: Constants.OCPP_ID_TAG)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.OCPP_ID_TAG)
                }

                if dictPayload["ocpp_in_transaction"] != nil {
                    UserDefaults.standard.set("\(dictPayload["ocpp_in_transaction"]!)", forKey: Constants.OCPP_IN_TRANSACTION)
                } else {
                    UserDefaults.standard.set("", forKey: Constants.OCPP_IN_TRANSACTION)
                }

                if dictPayload["mobile_pin"] != nil {
                    UserDefaults.standard.set("\(dictPayload["mobile_pin"]!)", forKey: Constants.MOBILE_PIN)
                    if "\(dictPayload["mobile_pin"]!)" == "" {
                        UserDefaults.standard.set("0", forKey: Constants.PIN)
                    } else {
                        UserDefaults.standard.set("1", forKey: Constants.PIN)
                    }
                } else {
                    UserDefaults.standard.set("", forKey: Constants.MOBILE_PIN)
                    UserDefaults.standard.set("0", forKey: Constants.PIN)
                }
            }
        } else if "\(dict!["event"]!)" == WebSocketDetails.EVENT_SCANQR && "\(dict!["type"]!)" == "success" {
            AppDelegate.shared.isFromNDCP = false
            showHideNDCP()
            let alert = UIAlertController(title: "Enter OTP to continue", message: "Your OTP is \(dict!["payload"]!)", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "OK", style: .default, handler: { _ in

                AppDelegate.shared.isFromOTPScan = "1"
                AppDelegate.shared.isFromStart = "0"

            }))
            self.navigationController?.present(alert, animated: true, completion: nil)

        } else if "\(dict!["event"]!)" == WebSocketDetails.EVENT_SCANQR && "\(dict!["type"]!)" == "NDCP" {
            print("NDCP event called")

            UserDefaults.standard.set("1", forKey: Constants.IS_NDCP)
            AppDelegate.shared.isFromNDCP = true
            showHideNDCP()
            let appDelegate = UIApplication.shared.delegate as! AppDelegate
            if let topController = appDelegate.window!.visibleViewController() {
                print("The view controller you're looking at is: \(topController)")
                let vc = ScanQRVC.instantiate(appStoryboard: .Charging)
                if topController == vc {
                    self.navigationController?.popViewController(animated: true)
                }
            }

        } else if "\(dict!["event"]!)" == WebSocketDetails.EVENT_TRANSACTION_START {

            AppDelegate.shared.isFromNDCP = false
            showHideNDCP()
            self.dismiss(animated: false, completion: nil)

            if (self.navigationController?.topViewController != self) {
//                let vc = HomeTabVC.instantiate(appStoryboard: .Home)
//                AppDelegate.shared.intPaymentTab = 1
//                self.navigationController?.pushViewController(vc, animated: true)
            }

            UserDefaults.standard.set("0", forKey: Constants.STR_SHOWMETER)
            let dictPayload = dict!["payload"] as! [String:Any]
            print(dictPayload)

            if "\(UserDefaults.standard.value(forKey: Constants.STR_SHOWMETER)!)" == "0" && AppDelegate.shared.strVehicleID != "" {
                print("Call API")
                AppDelegate.shared.strTransactionID = "\(dictPayload["transactionID"]!)"
                self.getTransVehicle(transID: "\(dictPayload["transactionID"]!)")
            }

            AppDelegate.shared.isStartEvent = true

            AppDelegate.shared.strHMS = "00:00:00"
            timer.invalidate()
            timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(onTransition1), userInfo: nil, repeats: true)

            stackNotCharging.isHidden = false

            self.availableBalanceLabel.text = "Available Balance : ₹ 0"
            self.totalChargesLabel.text = "₹ 0"
            self.startTimeLabel.text = "Start Time : \(AppDelegate.shared.strHMS)"
            self.consumedUnitsLabel.text = "0 kWh"

        } else if "\(dict!["event"]!)" == WebSocketDetails.EVENT_SHOW_METER_VALUE {

            if "\(UserDefaults.standard.value(forKey: Constants.IS_TXN_STOPPED)!)" == "0" {
                chargingButton.setTitle("Stop Charging", for: .normal)

                AppDelegate.shared.isFromNDCP = false
                showHideNDCP()
                let dictPayload = dict!["payload"] as! [String:Any]
                print(dictPayload)

                if "\(dictPayload["soc"]!)" != "0"  {
                    self.percentLabel.alpha = 1
                    let str = "\(dictPayload["soc"]!)"
                    if let n = NumberFormatter().number(from: str) {
                        let f = CGFloat(truncating: n)
    //                    chargeProgress.progressValue = f
                        self.percentLabel.text = "\(dictPayload["soc"]!)%"
                    }
    //                stackProgress.alpha = 1
    //                stackProgressHeight.constant = 30
                } else {
                    self.percentLabel.text = "\(dictPayload["soc"]!)%"
                    self.percentLabel.alpha = 0
    //                stackProgress.alpha = 0
    //                stackProgressHeight.constant = 0
    //                chargeProgress.progressValue = 0
                }

                AppDelegate.shared.strAvailBalance = Double("\(dictPayload["available_balance"]!)")!
                AppDelegate.shared.strCharges = Double("\(dictPayload["total_charge"]!)")!
//                let durationString = "\(dictPayload["hms"]!)" // or AppDelegate.shared.strTxnDuration
//
//                let components = durationString.split(separator: ":").compactMap { Int($0) }
//
//                if components.count == 3 {
//                    let hours = components[0]
//                    let minutes = components[1]
//                    let seconds = components[2]
//
//                    let totalSeconds = (hours * 3600) + (minutes * 60) + seconds
//
//                    let timeStampTxnID = Date(timeIntervalSince1970: TimeInterval(totalSeconds))
                AppDelegate.shared.strTxnDuration = "\(dictPayload["hms"]!)"

                let dateFormatter = DateFormatter()
                dateFormatter.timeZone = .current
                dateFormatter.locale = Locale(identifier: "en_US")
                dateFormatter.dateFormat = "HH:mm:ss"
                print("\(dictPayload["hms"]!)")

                AppDelegate.shared.strHMS = "\(dictPayload["hms"]!)"

                timer.invalidate()
                timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(onTransition1), userInfo: nil, repeats: true)

                if "\(UserDefaults.standard.value(forKey: Constants.IS_CHARGING)!)" == "0" {
                    UserDefaults.standard.set("1", forKey: Constants.IS_CHARGING)
                    timerCar.invalidate()
                    timerCar = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(onTransition), userInfo: nil, repeats: true)
                }

                AppDelegate.shared.strUsedUnit = Double("\(dictPayload["used_unit"]!)")!
                AppDelegate.shared.strChargePoint = "\(dictPayload["charge_station_name"] ?? "Nil")"
                AppDelegate.shared.strCSID = "\(dictPayload["charge_station_id"] ?? "Nil")"

                self.lblVehicle.text = "\(UserDefaults.standard.value(forKey: Constants.VEHICLE_NAME)!)"
                AppDelegate.shared.strVehicleID = "\(UserDefaults.standard.value(forKey: Constants.VEHICLE_ID)!)"

                stackNotCharging.isHidden = false

                self.availableBalanceLabel.text = "Available Balance : ₹ \(AppDelegate.shared.strAvailBalance.roundedDouble(digits: 2))"
                self.totalChargesLabel.text = "₹ \(AppDelegate.shared.strCharges.roundedDouble(digits: 2))"
                self.startTimeLabel.text = "Start Time : \(AppDelegate.shared.strHMS)"
                self.consumedUnitsLabel.text = "\(AppDelegate.shared.strUsedUnit) kWh"

                let nc = NotificationCenter.default
                nc.post(name: Notification.Name(WebSocketDetails.EVENT_SHOW_METER_VALUE), object: nil)
            } else {
                timer.invalidate()
                timerCar.invalidate()
                stackNotCharging.isHidden = false
                let nc = NotificationCenter.default
                nc.post(name: Notification.Name(WebSocketDetails.EVENT_TRANSACTION_STOPPED), object: nil)
            }

        } else if "\(dict!["event"]!)" == WebSocketDetails.EVENT_TRANSACTION_STOPPED {

            AppDelegate.shared.isFromNDCP = false
            showHideNDCP()
            chargingButton.setTitle("Start Charging", for: .normal)

            self.dismiss(animated: false, completion: nil)
            UserDefaults.standard.set("0", forKey: Constants.STR_SHOWMETER)
            let dictPayload = dict!["payload"] as! [String:Any]
            print("EVENT_TRANSACTION_STOPPED:-",dictPayload)
            AppDelegate.shared.strTransactionID = "\(dictPayload["transactionID"]!)"
            AppDelegate.shared.strStopStatus = "\(dictPayload["status"]!)"

            UserDefaults.standard.set("1", forKey: Constants.IS_CHARGING)
            timer.invalidate()
            timerCar.invalidate()

//            stackProgress.alpha = 0
//            stackProgressHeight.constant = 0
//            chargeProgress.progressValue = 0

            UserDefaults.standard.set("1", forKey: Constants.IS_TXN_STOPPED)
            print("IS_TXN_STOPPED:-","\(UserDefaults.standard.value(forKey: Constants.IS_TXN_STOPPED)!)")
            let nc = NotificationCenter.default
            nc.post(name: Notification.Name(WebSocketDetails.EVENT_TRANSACTION_STOPPED), object: nil)

            self.stackNotCharging.isHidden = true
            self.getTransactionInfo()

            AppDelegate.shared.transType = ""
            AppDelegate.shared.transValue = ""
            selectedChargeTypeValue = ""
            UserDefaults.standard.setValue("0", forKey: Constants.IS_NDCP)
            AppDelegate.shared.isStartEvent = false

        } else if "\(dict!["event"]!)" == WebSocketDetails.EVENT_REMOTE_STOP_TRANSACTION && "\(dict!["type"]!)" == "success" {
            UserDefaults.standard.setValue("0", forKey: Constants.IS_NDCP)
            AppDelegate.shared.isStartEvent = false
        } else if "\(dict!["event"]!)" == WebSocketDetails.EVENT_INSUFFICIENT_BALANCE {

            self.dismiss(animated: false, completion: nil)

//            stackProgress.alpha = 0
//            stackProgressHeight.constant = 0
//            chargeProgress.progressValue = 0

            AppDelegate.shared.isStartEvent = false
            let dictPayload = dict!["payload"] as! String
            let alert = UIAlertController(title: "Sorry", message: "\(dictPayload)", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "OK", style: .default, handler: { _ in

                UserDefaults.standard.set("0", forKey: Constants.STR_SHOWMETER)
                let appDelegate = UIApplication.shared.delegate as! AppDelegate
                if let topController = appDelegate.window!.visibleViewController() {
                    print("The view controller you're looking at is: \(topController)")
                    let vc = ScanQRVC.instantiate(appStoryboard: .Charging)
                    if topController == vc {
                        self.navigationController?.popViewController(animated: true)
                    }
                }
            }))
            self.navigationController?.present(alert, animated: true, completion: nil)
        }
    }

    func websocketDidReceiveData(socket: WebSocketClient, data: Data) {
        print("Received data: \(data.count)")
    }

}
extension ChargingVC: UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {

        if textField == txtUnitChargeType {
            let maxLength = 3
            let currentString: NSString = textField.text! as NSString
            let newString: NSString = currentString.replacingCharacters(in: range, with: string) as NSString
            return newString.length <= maxLength
        }
        return true
    }
}


